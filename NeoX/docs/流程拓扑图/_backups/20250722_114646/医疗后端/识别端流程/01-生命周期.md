```mermaid
graph TD
    A[客户端发起识别请求] --> B["apply()<br/>创建Task占位<br/>返回taskId"]
    B --> C["upload()<br/>上传处方图片<br/>Task进入待识别队列"]
    C --> D["PrescriptionAsyncDispatcher<br/>任务调度器<br/>分配QPS和识别进程"]
    D --> E["PrescriptionAsyncRecognize<br/>识别进程<br/>调用identify()进行OCR/QR识别"]
    E --> F["PrescriptionAsyncMerge<br/>合并进程<br/>判断是否需要合并多页处方"]
    F --> G["finishPrescription()<br/>处方入库<br/>结果写入Redis"]
    G --> H["客户端轮询<br/>qrListIds()<br/>获取识别结果"]
    H --> I{获取到结果?}
    I -->|是| J["qrNoticeDownloaded()<br/>确认下载完成<br/>清理Redis数据"]
    I -->|否| K[等待间隔]
    K --> H
    J --> L[识别完成]
    L --> M[等待下一次apply]
    M --> A

    style A fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style B fill:#6a1b9a,stroke:#fff,stroke-width:2px,color:#fff
    style C fill:#6a1b9a,stroke:#fff,stroke-width:2px,color:#fff
    style D fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style E fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style F fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style G fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style H fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style J fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style L fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
```

```mermaid
graph TD
    A[输入图片] --> B[加载图片]
    B --> C{分类模型过滤?}

    C -->|是| D[过滤无关图片]
    D --> E{是否需要旋转?}

    E -->|是| F[旋转图片]
    F --> G[继续处理]
    E -->|否| G
    C -->|否| G

    G --> H[分割模型处理]
    H --> I[结构检测模型处理]
    I --> J{是否识别QR码?}

    J -->|是| K[解码QR码]
    K --> L[处方结构处理<br/>头部/处方/尾部]
    J -->|否| L

    L --> M[OCR识别]
    M --> N[后处理与结果合并]
    N --> O[输出结果]

    style A fill:#2196f3,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#ffe089,stroke:#333,stroke-width:2px,color:#000
    style E fill:#ffe089,stroke:#333,stroke-width:2px,color:#000
    style J fill:#ffe089,stroke:#333,stroke-width:2px,color:#000
    style O fill:#4caf50,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style D fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style F fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style G fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style H fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style I fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style K fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style L fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style M fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
    style N fill:#e3f2fd,stroke:#333,stroke-width:1.5px,color:#000
```

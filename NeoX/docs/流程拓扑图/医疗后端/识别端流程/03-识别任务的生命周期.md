```mermaid
graph TD
    subgraph "Phase 1: 任务创建与上传"
        A1[客户端请求<br/>originFileName, pageCount, receptionId]
        A2[创建Task记录<br/>status: WAIT_UPLOAD<br/>taskId生成]
        A3[图片上传<br/>imagePath, imageList<br/>pretreatment数据]
        A4[状态更新<br/>status: WAIT_RECOGNIZE<br/>processTime.uploaded]
    end

    subgraph "Phase 2: 任务调度"
        B1[Dispatcher扫描<br/>status: WAIT_RECOGNIZE]
        B2[分配资源<br/>qpsId, processId]
        B3[状态更新<br/>status: DISPATCHED]
        B4[写入临时表<br/>p_task_temp]
    end

    subgraph "Phase 3: 识别处理"
        C1[识别进程获取任务<br/>从临时表]
        C2[OCR/QR识别<br/>PrescriptionTrait::identify]
        C3[生成识别结果<br/>presData序列化]
        C4[状态更新<br/>status: RECOGNIZED<br/>processTime.recognized]
    end

    subgraph "Phase 4: 合并判断"
        D1[Merge进程扫描<br/>status: RECOGNIZED]
        D2[合并逻辑判断<br/>姓名、生日、医生匹配]
        D3[合并处方<br/>mergePrescription]
        D4[状态更新<br/>status: MERGED<br/>parentId设置]
    end

    subgraph "Phase 5: 完成入库"
        E1[处方完成<br/>finishPrescription]
        E2[数据入库<br/>prescription_original]
        E3[Redis写入<br/>识别结果缓存]
        E4[通知生成<br/>addNotice]
    end

    subgraph "Phase 6: 客户端获取"
        F1[轮询请求<br/>qrListIds with timestamp]
        F2[Redis查询<br/>按时间戳范围获取]
        F3[返回结果<br/>qrList, merge, finish]
        F4[确认下载<br/>qrNoticeDownloaded]
        F5[Redis清理<br/>deleteRedisTaskByIds]
    end

    A1 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1

    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> D1

    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> E1

    E1 --> E2
    E2 --> E3
    E3 --> E4
    E4 --> F1

    F1 --> F2
    F2 --> F3
    F3 --> F4
    F4 --> F5

    style A2 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style A4 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style B3 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style C4 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style D4 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style E2 fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style E3 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style F3 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
```

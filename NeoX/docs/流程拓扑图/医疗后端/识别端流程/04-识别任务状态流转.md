```mermaid
graph TD
    subgraph "Task Status Flow 任务状态流转"
        A[TASK_STATUS_WAIT_UPLOAD<br/>等待上传]
        B[TASK_STATUS_WAIT_RECOGNIZE<br/>等待识别]
        C[TASK_STATUS_DISPATCHED<br/>已分发]
        D[TASK_STATUS_RECOGNIZING<br/>识别中]
        E[TASK_STATUS_RECOGNIZED<br/>识别完成]
        F[TASK_STATUS_MERGED<br/>已合并]
        G[TASK_STATUS_VIEWED<br/>已查看]
        H[TASK_STATUS_WORKED<br/>已处理]

        FAIL1[TASK_STATUS_FAILED_CONVERT_IMG<br/>图片转换失败]
        FAIL2[TASK_STATUS_RECOGNIZE_FAILED<br/>识别失败]
        FAIL3[TASK_STATUS_ENQUETE<br/>问卷调查]
    end

    subgraph "Redis Status Redis状态"
        R1[QR_LIST_STATUS_DOING<br/>处理中]
        R2[QR_LIST_STATUS_SUCCESS<br/>成功]
        R3[QR_LIST_STATUS_QR<br/>QR识别]
        R4[QR_LIST_STATUS_FAILED<br/>失败]
        R5[QR_LIST_STATUS_MERGED<br/>已合并]
    end

    subgraph "Process Events 处理事件"
        P1["apply()"]
        P2["uploadFile()"]
        P3["Dispatcher分发"]
        P4["Recognition识别"]
        P5["Merge合并"]
        P6["finishPrescription()"]
        P7["客户端查看"]
        P8["客户端处理"]
    end

    P1 --> A
    A --> P2
    P2 --> B
    B --> P3
    P3 --> C
    C --> P4
    P4 --> D
    D --> E
    E --> P5
    P5 --> F
    F --> P6
    P6 --> G
    G --> P7
    P7 --> H
    H --> P8

    P2 --> FAIL1
    P4 --> FAIL2
    P2 --> FAIL3

    A --> R1
    E --> R2
    E --> R3
    FAIL1 --> R4
    FAIL2 --> R4
    F --> R5

    style A fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style B fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style C fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style D fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style E fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style F fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style G fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style H fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style FAIL1 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style FAIL2 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style FAIL3 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
```

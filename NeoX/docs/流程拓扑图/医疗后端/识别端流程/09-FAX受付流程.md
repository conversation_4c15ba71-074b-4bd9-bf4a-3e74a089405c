```mermaid
graph TD
    A[PreSentReceptionEvent 事件触发] --> B[handle方法开始]
    B --> C[调用findOldReceptionByPresObj]

    C --> D[调用findNsipsOriginalByPrescription<br/>查找NSIPS原稿数据]
    D --> E{找到NSIPS数据?}

    E -->|否| F[返回null]
    E -->|是| G[调用getReceptionByNsipsId<br/>查找对应受付]

    G --> H[直接通过nsipsId查找受付<br/>条件：merchantId + source=NSIPS + nsipsId]
    H --> I{找到受付?}

    I -->|是| J[返回找到的受付]
    I -->|否| K[查找NsipsComparison记录]

    K --> L{找到NsipsComparison?}
    L -->|否| M[返回null]
    L -->|是| N{prescriptionOriginalId为空?}

    N -->|是| O[返回null]
    N -->|否| P[通过prescriptionOriginalId查找受付]

    P --> Q{找到受付?}
    Q -->|是| R[返回找到的受付]
    Q -->|否| S[返回null]

    J --> T[创建新受付分支]
    R --> T
    S --> U[创建新受付]
    M --> U
    O --> U

    U --> V[调用ReceptionLibrary.createReceptionByNsipsModel]
    V --> W[调用QueueLib.addNumberByReceptionId<br/>标记为复诊REVISIT_FLAG_REVISIT_1]
    W --> X[返回新创建的受付]

    T --> Y[设置oldReceptionId]
    X --> Y
    F --> Z[记录未找到NSIPS数据日志]

    Y --> AA[保存reception对象]
    AA --> BB[查找关联队列号]
    BB --> CC[记录处理成功日志]

    Z --> DD[处理完成]
    CC --> DD

    B --> EE{发生异常?}
    EE -->|是| FF[记录错误日志并重新抛出异常]

    subgraph "NSIPS数据匹配条件"
        GG[患者姓名汉字匹配 OR 患者姓名假名匹配]
        HH[生日匹配]
        II[创建时间匹配]
        JJ[三个条件必须同时满足]
        GG --> JJ
        HH --> JJ
        II --> JJ
    end

    style A fill:#1976d2,stroke:#333,stroke-width:2px,color:#fff
    style J fill:#2e7d32,stroke:#333,stroke-width:2px,color:#fff
    style R fill:#2e7d32,stroke:#333,stroke-width:2px,color:#fff
    style X fill:#2e7d32,stroke:#333,stroke-width:2px,color:#fff
    style F fill:#d32f2f,stroke:#333,stroke-width:2px,color:#fff
    style M fill:#d32f2f,stroke:#333,stroke-width:2px,color:#fff
    style O fill:#d32f2f,stroke:#333,stroke-width:2px,color:#fff
    style S fill:#d32f2f,stroke:#333,stroke-width:2px,color:#fff
    style DD fill:#7b1fa2,stroke:#333,stroke-width:2px,color:#fff
    style U fill:#f57c00,stroke:#333,stroke-width:2px,color:#fff
    style Y fill:#388e3c,stroke:#333,stroke-width:2px,color:#fff
    style AA fill:#388e3c,stroke:#333,stroke-width:2px,color:#fff
    style BB fill:#388e3c,stroke:#333,stroke-width:2px,color:#fff
    style CC fill:#388e3c,stroke:#333,stroke-width:2px,color:#fff
    style JJ fill:#0277bd,stroke:#333,stroke-width:2px,color:#fff
```

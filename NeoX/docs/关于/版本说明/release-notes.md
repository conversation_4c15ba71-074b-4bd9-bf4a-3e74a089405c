# 版本说明

本文档记录了 NeoX 文档系统的版本更新历史和重要变更。

## v2025072101（2025年7月21日 第1版）

### 🚀 新功能

- **流程拓扑图章节**

  - 新增医疗后端识别端流程详细文档，包含生命周期、架构、状态流转等

  - 新增 FAX 受付流程、GPU Engine、NSIPS、Smart Merge 等医疗后端相关流程

  - 新增薬師丸賢太处方笺保存匹配流程文档

  - 新增自动化运维相关流程文档，包括 medical-backend、performance、terraform

- **自动化运维章节**

  - 新增 Ansible Semaphore 自动化发布平台配置指南

### 📝 内容改进

- **文档导航结构优化**

  - 重新组织导航结构，增加主要章节分类

  - 优化子章节组织，提高文档查找效率

  - 调整文档层级，使结构更加清晰

- **首页内容更新**

  - 更新首页导航表格，增加新增章节的快速链接

  - 完善项目结构描述，反映最新的文件组织

  - 优化"如何使用本文档"部分，增加流程了解和自动化部署指南

### 🔧 文档结构完善

- **导航配置更新**

  - 在 `mkdocs.yml` 中完善导航配置，增加新章节

  - 调整章节顺序，优化用户浏览体验

  - 保持导航结构与实际文件结构一致

- **README 同步更新**

  - 更新项目主要内容描述，增加自动化运维和流程拓扑图内容

  - 完善项目结构图，反映最新的目录组织

  - 增加获取帮助部分的常见问题解答链接

### 🎯 文档覆盖范围扩展

- **系统架构文档**：新增多个系统架构和流程图文档，帮助开发者理解系统整体结构

- **自动化工具文档**：增加自动化部署和运维相关工具的使用指南

- **流程说明文档**：详细说明各个子系统的工作流程和数据流转

---

**发布日期**：2025年7月21日  
**版本类型**：功能扩展版本  
**维护团队**：NeoX 开发团队

## v2025063001（2025年6月30日 第1版）

### 🚀 新功能

- **常见问题解答（FAQ）**

  - 新增详细的FAQ文档，涵盖开发环境搭建中的常见问题

  - 包含Docker容器报错、API请求鉴权、网络连接问题等解决方案

  - 提供Windows系统特有问题的解决方法

### 📝 内容改进

- **项目描述优化**

  - 简化项目介绍，提高可读性

  - 更新仓库地址为具体的Bitbucket链接

  - 优化项目结构描述，使用通用格式

- **使用方式重构**

  - 重新组织README.md中的使用者和开发者说明

  - 使用者现在可直接打开HTML文件，无需安装任何工具

  - 开发者提供多种安装和启动方式选择

### 🔧 文档结构完善

- **导航优化**

  - 在主页添加FAQ章节的快速访问链接

  - 更新文档导航表格，包含常见问题解答条目

  - 保持文档结构的一致性和完整性

### 🎯 问题解决覆盖

FAQ文档包含以下常见问题的解决方案：

- **Docker容器报错处理**：composer扩展安装和.env配置

- **API请求鉴权设置**：公私钥对配置和Postman使用

- **ImageMagick挂载错误**：policy.xml挂载问题解决

- **Windows网络连接问题**：代理设置和手动安装方法

- **Composer安装报错**：依赖冲突和SSH密钥配置

---

**发布日期**：2025年6月30日  
**版本类型**：功能增强版本  
**维护团队**：NeoX 开发团队

## v2025062801（2025年6月28日 第1版）

### 🚀 新功能

- **文档系统初始化**

  - 建立了完整的开发文档体系

  - 采用 MkDocs Material 主题，提供现代化的文档界面

  - 支持中文本地化显示

- **后端开发环境搭建指南**

  - 详细的代码部署流程说明

  - AWS ECR 访问权限配置指导

  - Docker 开发环境部署步骤

  - 后端代码环境配置详解

### 📚 文档结构

- **开发环境搭建**

  - 后端开发完整流程文档

  - 分模块详细说明，便于查阅和维护

- **版本管理**

  - 建立规范的版本说明文档

  - 采用日期版本号格式（YYYYMMDDXX）

### 🔧 技术特性

- **文档框架**：MkDocs + Material 主题

- **语言支持**：完整中文支持

- **导航结构**：层级化章节组织

- **搜索功能**：全文搜索支持

- **响应式设计**：适配各种设备屏幕

### 📝 文档覆盖范围

- **代码部署**：Git 仓库管理和代码获取

- **权限配置**：AWS ECR 访问权限设置

- **环境搭建**：Docker 开发环境部署

- **项目配置**：后端服务配置和验证

### 🎯 下一步计划

- 添加更多开发环境配置说明

- 完善API文档

- 增加常见问题解答（FAQ）

- 补充最佳实践指南

---

**发布日期**：2025年6月28日  
**版本类型**：初始版本  
**维护团队**：NeoX 开发团队
